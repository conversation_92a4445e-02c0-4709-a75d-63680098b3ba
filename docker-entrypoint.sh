#!/bin/bash
set -e

echo "Starting Laravel application setup..."

# Wait for database to be ready (optional - uncomment if using separate DB container)
# echo "Waiting for database..."
# while ! nc -z $DB_HOST $DB_PORT; do sleep 1; done

# Clear all caches
echo "Clearing application caches..."
php artisan config:clear || true
php artisan route:clear || true
php artisan view:clear || true
php artisan cache:clear || true

# Create storage link
echo "Creating storage link..."
php artisan storage:link || true

# Run database migrations
echo "Running database migrations..."
php artisan migrate --force

# Verify Livewire configuration before caching
echo "Verifying Livewire configuration..."
php -r "
require 'vendor/autoload.php';
\$app = require 'bootstrap/app.php';
\$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();
\$disk = config('livewire.temporary_file_upload.disk');
echo 'Livewire upload disk: ' . (\$disk ?: 'default') . PHP_EOL;
if (\$disk === 'local') {
    echo '✅ GOOD: Using local storage (no CORS issues)' . PHP_EOL;
} else {
    echo '❌ ISSUE: Using ' . \$disk . ' storage (will cause CORS errors)' . PHP_EOL;
}
"

# Cache configuration for production
echo "Caching configuration..."
php artisan config:cache
php artisan route:cache
php artisan view:cache

echo "Laravel application setup completed!"

# Start the Laravel development server
echo "Starting Laravel development server..."
exec php artisan serve --host=0.0.0.0 --port=8000
