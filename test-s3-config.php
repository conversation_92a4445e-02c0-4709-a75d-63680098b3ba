<?php

/**
 * S3 Configuration Test Script
 * Run this script to test S3 connectivity and permissions
 */

require_once __DIR__ . '/vendor/autoload.php';

use Illuminate\Support\Facades\Storage;
use Aws\S3\S3Client;
use Aws\Exception\AwsException;

echo "=== S3 Configuration Test ===\n\n";

// Test 1: Check AWS Environment Variables
echo "1. AWS Environment Variables:\n";
$awsKey = env('AWS_ACCESS_KEY_ID');
$awsSecret = env('AWS_SECRET_ACCESS_KEY');
$awsRegion = env('AWS_DEFAULT_REGION');
$awsBucket = env('AWS_BUCKET');
$awsUrl = env('AWS_URL');

echo "   AWS_ACCESS_KEY_ID: " . ($awsKey ? substr($awsKey, 0, 8) . '...' : 'NOT SET') . "\n";
echo "   AWS_SECRET_ACCESS_KEY: " . ($awsSecret ? 'SET (hidden)' : 'NOT SET') . "\n";
echo "   AWS_DEFAULT_REGION: " . ($awsRegion ?: 'NOT SET') . "\n";
echo "   AWS_BUCKET: " . ($awsBucket ?: 'NOT SET') . "\n";
echo "   AWS_URL: " . ($awsUrl ?: 'NOT SET') . "\n";

if (!$awsKey || !$awsSecret || !$awsRegion || !$awsBucket) {
    echo "   ❌ ERROR: Missing required AWS configuration\n";
} else {
    echo "   ✅ OK: AWS configuration present\n";
}
echo "\n";

// Test 2: Check Default Filesystem
echo "2. Filesystem Configuration:\n";
$defaultDisk = config('filesystems.default');
$livewireDisk = config('livewire.temporary_file_upload.disk') ?: $defaultDisk;

echo "   Default filesystem: " . $defaultDisk . "\n";
echo "   Livewire upload disk: " . $livewireDisk . "\n";

if ($livewireDisk === 's3' && (!$awsKey || !$awsSecret)) {
    echo "   ❌ ERROR: Livewire using S3 but AWS credentials missing\n";
} else {
    echo "   ✅ OK: Filesystem configuration looks good\n";
}
echo "\n";

// Test 3: Test S3 Connection (if S3 is configured)
if ($awsKey && $awsSecret && $awsRegion && $awsBucket) {
    echo "3. S3 Connection Test:\n";
    
    try {
        $s3Client = new S3Client([
            'version' => 'latest',
            'region' => $awsRegion,
            'credentials' => [
                'key' => $awsKey,
                'secret' => $awsSecret,
            ],
        ]);

        // Test bucket access
        $result = $s3Client->headBucket(['Bucket' => $awsBucket]);
        echo "   ✅ OK: Can access S3 bucket '$awsBucket'\n";

        // Test write permissions
        $testKey = 'livewire-test-' . time() . '.txt';
        $s3Client->putObject([
            'Bucket' => $awsBucket,
            'Key' => $testKey,
            'Body' => 'Livewire test file',
        ]);
        echo "   ✅ OK: Can write to S3 bucket\n";

        // Clean up test file
        $s3Client->deleteObject([
            'Bucket' => $awsBucket,
            'Key' => $testKey,
        ]);
        echo "   ✅ OK: Can delete from S3 bucket\n";

    } catch (AwsException $e) {
        echo "   ❌ ERROR: S3 connection failed - " . $e->getMessage() . "\n";
        
        if (strpos($e->getMessage(), 'InvalidAccessKeyId') !== false) {
            echo "   💡 HINT: Check AWS_ACCESS_KEY_ID\n";
        } elseif (strpos($e->getMessage(), 'SignatureDoesNotMatch') !== false) {
            echo "   💡 HINT: Check AWS_SECRET_ACCESS_KEY\n";
        } elseif (strpos($e->getMessage(), 'NoSuchBucket') !== false) {
            echo "   💡 HINT: Check AWS_BUCKET name and region\n";
        } elseif (strpos($e->getMessage(), 'AccessDenied') !== false) {
            echo "   💡 HINT: AWS credentials don't have S3 permissions\n";
        }
    } catch (Exception $e) {
        echo "   ❌ ERROR: " . $e->getMessage() . "\n";
    }
} else {
    echo "3. S3 Connection Test: SKIPPED (AWS not configured)\n";
}
echo "\n";

// Test 4: Test Local Storage (fallback)
echo "4. Local Storage Test:\n";
try {
    $testContent = 'Livewire local test - ' . time();
    $testPath = 'livewire-test.txt';
    
    Storage::disk('local')->put($testPath, $testContent);
    echo "   ✅ OK: Can write to local storage\n";
    
    $retrieved = Storage::disk('local')->get($testPath);
    if ($retrieved === $testContent) {
        echo "   ✅ OK: Can read from local storage\n";
    } else {
        echo "   ❌ ERROR: Local storage read/write mismatch\n";
    }
    
    Storage::disk('local')->delete($testPath);
    echo "   ✅ OK: Can delete from local storage\n";
    
} catch (Exception $e) {
    echo "   ❌ ERROR: Local storage test failed - " . $e->getMessage() . "\n";
}
echo "\n";

// Test 5: Recommendations
echo "5. Recommendations:\n";

if ($livewireDisk === 's3' && $awsKey && $awsSecret) {
    echo "   📝 Using S3 for Livewire uploads\n";
    echo "   📝 Ensure AWS credentials have s3:PutObject, s3:GetObject, s3:DeleteObject permissions\n";
    echo "   📝 Consider using IAM roles instead of access keys in production\n";
} elseif ($livewireDisk === 'local') {
    echo "   📝 Using local storage for Livewire uploads (recommended for temporary files)\n";
    echo "   📝 Ensure storage/app directory is writable\n";
    echo "   📝 Consider cleanup of temporary files\n";
} else {
    echo "   ⚠️  WARNING: Unusual disk configuration for Livewire uploads\n";
}

echo "\n=== Test Complete ===\n";
echo "If S3 tests fail, consider using 'local' disk for Livewire temporary uploads.\n";
echo "Livewire only needs temporary storage - final files can still use S3.\n";
