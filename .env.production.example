# Production Environment Configuration for dhtenders.com
APP_NAME="E-Tender Portal"
APP_ENV=production
APP_KEY=base64:Qbn6qD/nLWUOdZymW92WfoekIFR4ufTT7QeyF7WtEM0=
APP_DEBUG=false
APP_URL=https://dhtenders.com
LOG_CHANNEL=stack
LOG_LEVEL=error

# Database Configuration
#DB_CONNECTION=mysql
#DB_HOST=etender.c3iq0qo2oiuq.ap-south-1.rds.amazonaws.com
#DB_PORT=3306
#DB_DATABASE=etender
#DB_USERNAME=etender
#DB_PASSWORD="dimahasaoIT#"

# Cache and Session Configuration
BROADCAST_DRIVER=log
CACHE_DRIVER=file
FILESYSTEM_DRIVER=local
QUEUE_CONNECTION=sync
SESSION_DRIVER=file
SESSION_LIFETIME=120
SESSION_DOMAIN=.dhtenders.com
SESSION_SECURE_COOKIE=true
SESSION_SAME_SITE_COOKIE=lax

# Redis Configuration
REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

# Mail Configuration
MAIL_MAILER=smtp
MAIL_HOST=mailhog
MAIL_PORT=1025
MAIL_USERNAME=null
MAIL_PASSWORD=null
MAIL_ENCRYPTION=null
MAIL_FROM_ADDRESS=null
MAIL_FROM_NAME="${APP_NAME}"

# AWS Configuration
AWS_ACCESS_KEY_ID=********************
AWS_SECRET_ACCESS_KEY=wQpBTljFbgTampiXXB3yxwxHN6Bdx+BjkvC3wxtz
AWS_DEFAULT_REGION=us-east-1
AWS_BUCKET=etender
AWS_URL=https://etender.s3.us-east-1.amazonaws.com
AWS_USE_PATH_STYLE_ENDPOINT=false

# Pusher Configuration
PUSHER_APP_ID=
PUSHER_APP_KEY=
PUSHER_APP_SECRET=
PUSHER_APP_CLUSTER=mt1

MIX_PUSHER_APP_KEY="${PUSHER_APP_KEY}"
MIX_PUSHER_APP_CLUSTER="${PUSHER_APP_CLUSTER}"

# Logging Configuration
LOG_STACK=single,flare
FLARE_KEY=PqSllVUaCJHXfq03v9BhxpZiplv5ZUUO

# Razorpay Configuration
RAZOR_PAY_MERCHANT_ID=your_merchant_id_here
RAZOR_PAY_KEY=your_razorpay_key_here
RAZOR_PAY_SECRET=your_razorpay_secret_here
RAZOR_PAY_WEBHOOK_SECRET=your_webhook_secret_here

# Sanctum Configuration for Production
SANCTUM_STATEFUL_DOMAINS=dhtenders.com,www.dhtenders.com
