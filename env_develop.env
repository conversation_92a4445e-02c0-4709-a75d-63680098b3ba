APP_NAME="E-Tender Portal"
APP_ENV=develop
APP_KEY=base64:Qbn6qD/nLWUOdZymW92WfoekIFR4ufTT7QeyF7WtEM0=
APP_DEBUG=false
APP_URL=https://www.dhtenders.com
LOG_CHANNEL=stack
LOG_LEVEL=debug

#DB_CONNECTION=mysql
#DB_HOST=etender.c3iq0qo2oiuq.ap-south-1.rds.amazonaws.com
#DB_PORT=3306
#DB_DATABASE=etender
#DB_USERNAME=etender
#DB_PASSWORD="dimahasaoIT#"

BROADCAST_DRIVER=log
CACHE_DRIVER=file
FILESYSTEM_DRIVER=local
QUEUE_CONNECTION=sync
SESSION_DRIVER=file
SESSION_LIFETIME=120

# Livewire Configuration - CRITICAL for avoiding S3 CORS issues
LIVEWIRE_TEMPORARY_UPLOAD_DISK=local

MEMCACHED_HOST=127.0.0.1

REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

MAIL_MAILER=smtp
MAIL_HOST=mailhog
MAIL_PORT=1025
MAIL_USERNAME=null
MAIL_PASSWORD=null
MAIL_ENCRYPTION=null
MAIL_FROM_ADDRESS=null
MAIL_FROM_NAME="${APP_NAME}"

AWS_ACCESS_KEY_ID=********************
AWS_SECRET_ACCESS_KEY=SfoJbcOJ1i6bSukyK9nwQ4dwO/v2fwuMy3ZCW73E
AWS_DEFAULT_REGION=ap-south-1
AWS_BUCKET=etender-dataset
AWS_USE_PATH_STYLE_ENDPOINT=false
AWS_URL=https://etender-dataset.s3.ap-south-1.amazonaws.com


PUSHER_APP_ID=
PUSHER_APP_KEY=
PUSHER_APP_SECRET=
PUSHER_APP_CLUSTER=mt1

MIX_PUSHER_APP_KEY="${PUSHER_APP_KEY}"
MIX_PUSHER_APP_CLUSTER="${PUSHER_APP_CLUSTER}"
LOG_CHANNEL=stack
LOG_STACK=single,flare
FLARE_KEY=PqSllVUaCJHXfq03v9BhxpZiplv5ZUUO

# Razorpay Configuration
RAZOR_PAY_MERCHANT_ID=QUKrMxOM0XTjbi
RAZOR_PAY_KEY=***********************
RAZOR_PAY_SECRET=bytELuQaUNikGeU3NsM7IlQR
RAZOR_PAY_WEBHOOK_SECRET=dhtenders


# Sanctum Configuration for Production
SANCTUM_STATEFUL_DOMAINS=dhtenders.com,www.dhtenders.com