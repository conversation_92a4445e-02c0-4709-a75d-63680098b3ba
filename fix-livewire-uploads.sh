#!/bin/bash

echo "🔧 Fixing Livewire Upload CORS Issues..."
echo "========================================"

# Clear Laravel caches
echo "1. Clearing Laravel caches..."
php artisan config:clear
php artisan route:clear
php artisan view:clear
php artisan cache:clear

# Check Livewire configuration
echo ""
echo "2. Checking Livewire configuration..."
php -r "
require 'vendor/autoload.php';
\$app = require 'bootstrap/app.php';
\$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();
\$config = config('livewire.temporary_file_upload.disk');
echo 'Livewire upload disk: ' . (\$config ?: 'default') . PHP_EOL;
if (\$config === 'local') {
    echo '✅ GOOD: Using local storage (no CORS issues)' . PHP_EOL;
} elseif (\$config === 's3') {
    echo '❌ ISSUE: Still using S3 (will cause CORS errors)' . PHP_EOL;
    echo '💡 FIX: Change disk to \"local\" in config/livewire.php' . PHP_EOL;
} else {
    echo '⚠️  WARNING: Using default disk (' . config('filesystems.default') . ')' . PHP_EOL;
}
"

# Check if storage directory is writable
echo ""
echo "3. Checking storage permissions..."
if [ -w "storage/app" ]; then
    echo "✅ GOOD: storage/app is writable"
else
    echo "❌ ISSUE: storage/app is not writable"
    echo "💡 FIX: Run 'chmod -R 775 storage/'"
fi

# Create livewire-tmp directory if it doesn't exist
echo ""
echo "4. Ensuring livewire-tmp directory exists..."
mkdir -p storage/app/livewire-tmp
chmod 775 storage/app/livewire-tmp
echo "✅ DONE: livewire-tmp directory ready"

echo ""
echo "🎉 Fix Complete!"
echo "==============="
echo ""
echo "Next steps:"
echo "1. Restart your development server"
echo "2. Hard refresh your browser (Ctrl+F5 or Cmd+Shift+R)"
echo "3. Try uploading a file"
echo "4. Check browser console - should see NO S3 CORS errors"
echo ""
echo "If you still see S3 URLs in upload requests:"
echo "- Verify config/livewire.php has 'disk' => 'local'"
echo "- Clear browser cache completely"
echo "- Check that you restarted the server"
