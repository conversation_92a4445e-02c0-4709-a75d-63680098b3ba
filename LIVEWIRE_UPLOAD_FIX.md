# Livewire File Upload 401 Unauthorized Fix

## Problem
Getting `POST https://dhtenders.com/livewire/upload-file?expires=1749809052&signature=be31d6f… 401 (Unauthorized)` error on production while file uploads work fine locally.

## Root Cause
The issue was caused by **S3 filesystem configuration problems** and session/domain issues:

### **Primary Issue: S3 Filesystem Configuration**
1. **AWS Credentials Mismatch** - Different AWS credentials between local and production
2. **Missing AWS_URL** - Production environment missing AWS_URL configuration
3. **S3 Permissions** - AWS credentials may lack proper S3 permissions for temporary uploads
4. **Livewire Disk Configuration** - Livewire using default disk which may be S3 in production

### **Secondary Issues: Session/Domain Configuration**
5. **Missing APP_URL configuration** - Production environment didn't have proper APP_URL set
6. **Session domain mismatch** - Session cookies weren't configured for the production domain
7. **HTTPS/Secure cookie settings** - Production uses HTTPS but session cookies weren't configured as secure
8. **CORS configuration** - Livewire endpoints weren't included in CORS paths for credential support

## Solution

### **Primary Fix: S3 Filesystem Configuration**

### 1. Updated Livewire Configuration (`config/livewire.php`)
- Changed `'disk' => null` to `'disk' => 'local'` for temporary uploads
- This avoids S3 permission issues for temporary files
- Final file storage can still use S3 through specific disk configurations

### 2. Updated Production Environment (`.env.production.example`)
- Added missing `AWS_URL` configuration
- Ensured AWS credentials match production S3 bucket
- Set proper AWS region configuration

### **Secondary Fixes: Session/Domain Configuration**

### 3. Updated Session Configuration (`config/session.php`)
- Made `SESSION_SECURE_COOKIE` configurable via environment variable (defaults to false for local)
- Made `SESSION_SAME_SITE_COOKIE` configurable via environment variable

### 4. Updated CORS Configuration (`config/cors.php`)
- Added `livewire/*` to allowed paths
- Enabled `supports_credentials` for session-based authentication

### 5. Updated Sanctum Configuration (`config/sanctum.php`)
- Added production domains (`dhtenders.com`, `www.dhtenders.com`) to stateful domains

## Deployment Steps

### 1. Update Production Environment Variables
Copy the settings from `.env.production.example` to your production environment:

```bash
# Core Configuration
APP_URL=https://dhtenders.com

# AWS S3 Configuration (CRITICAL)
AWS_ACCESS_KEY_ID=********************
AWS_SECRET_ACCESS_KEY=wQpBTljFbgTampiXXB3yxwxHN6Bdx+BjkvC3wxtz
AWS_DEFAULT_REGION=us-east-1
AWS_BUCKET=etender
AWS_URL=https://etender.s3.us-east-1.amazonaws.com

# Session Configuration
SESSION_DOMAIN=.dhtenders.com
SESSION_SECURE_COOKIE=true
SESSION_SAME_SITE_COOKIE=lax

# Sanctum Configuration
SANCTUM_STATEFUL_DOMAINS=dhtenders.com,www.dhtenders.com
```

### 2. Deploy the Code Changes
Use your existing deployment process:

```bash
./deploy.sh
```

### 3. Clear Caches (if needed)
If you have access to the production server:

```bash
php artisan config:clear
php artisan config:cache
php artisan route:clear
php artisan route:cache
```

## Testing

After deployment, test the following:

1. **Login to the application** on production
2. **Navigate to a page with file upload** (e.g., tender document upload)
3. **Try uploading a file** - should work without 401 errors
4. **Check browser network tab** - Livewire upload requests should return 200 OK

## Troubleshooting

### **S3-Related Issues:**
1. **Run S3 test script**: `php test-s3-config.php` to verify S3 connectivity
2. **Check AWS credentials** - Ensure they have S3 permissions (s3:PutObject, s3:GetObject, s3:DeleteObject)
3. **Verify S3 bucket** - Ensure bucket exists and is in the correct region
4. **Check AWS_URL** - Must match the bucket region and name

### **Session-Related Issues:**
5. **Check browser cookies** - Ensure session cookies are being set with correct domain
6. **Verify APP_URL** - Must match the actual domain (https://dhtenders.com)
7. **Check HTTPS** - Ensure all requests are over HTTPS in production
8. **Session storage** - Verify session files are writable in production
9. **Check logs** - Look for authentication or session-related errors

### **Quick Fix:**
If S3 issues persist, Livewire is now configured to use local storage for temporary uploads, which should resolve most 401 errors.

## Files Modified

- `config/livewire.php` - **CRITICAL**: Changed disk from null to 'local' for temporary uploads
- `config/session.php` - Session security and domain configuration
- `config/cors.php` - CORS configuration for Livewire
- `config/sanctum.php` - Stateful domains for production
- `.env.production.example` - Production environment template with AWS_URL
- `test-s3-config.php` - S3 connectivity test script

## Security Notes

- File uploads use existing authentication through Livewire's web middleware group
- Session cookies are secure in production (HTTPS only) when SESSION_SECURE_COOKIE=true
- CSRF protection is maintained through the web middleware group
- Session domain is properly scoped to prevent cookie leakage
- CORS credentials support enabled for proper session handling
