# Livewire File Upload 401 Unauthorized Fix

## Problem
Getting `POST https://dhtenders.com/livewire/upload-file?expires=1749809052&signature=be31d6f… 401 (Unauthorized)` error on production while file uploads work fine locally.

## Root Cause
The issue was caused by session and domain configuration problems:

1. **Missing APP_URL configuration** - Production environment didn't have proper APP_URL set
2. **Session domain mismatch** - Session cookies weren't configured for the production domain
3. **HTTPS/Secure cookie settings** - Production uses HTTPS but session cookies weren't configured as secure
4. **CORS configuration** - Livewire endpoints weren't included in CORS paths for credential support

## Solution

### 1. Updated Session Configuration (`config/session.php`)
- Made `SESSION_SECURE_COOKIE` configurable via environment variable (defaults to false for local)
- Made `SESSION_SAME_SITE_COOKIE` configurable via environment variable

### 2. Updated CORS Configuration (`config/cors.php`)
- Added `livewire/*` to allowed paths
- Enabled `supports_credentials` for session-based authentication

### 3. Updated Sanctum Configuration (`config/sanctum.php`)
- Added production domains (`dhtenders.com`, `www.dhtenders.com`) to stateful domains

### 4. Created Production Environment Template (`.env.production.example`)
- Proper APP_URL configuration
- Session domain and security settings
- Sanctum stateful domains configuration

## Deployment Steps

### 1. Update Production Environment Variables
Copy the settings from `.env.production.example` to your production environment:

```bash
APP_URL=https://dhtenders.com
SESSION_DOMAIN=.dhtenders.com
SESSION_SECURE_COOKIE=true
SESSION_SAME_SITE_COOKIE=lax
SANCTUM_STATEFUL_DOMAINS=dhtenders.com,www.dhtenders.com
```

### 2. Deploy the Code Changes
Use your existing deployment process:

```bash
./deploy.sh
```

### 3. Clear Caches (if needed)
If you have access to the production server:

```bash
php artisan config:clear
php artisan config:cache
php artisan route:clear
php artisan route:cache
```

## Testing

After deployment, test the following:

1. **Login to the application** on production
2. **Navigate to a page with file upload** (e.g., tender document upload)
3. **Try uploading a file** - should work without 401 errors
4. **Check browser network tab** - Livewire upload requests should return 200 OK

## Troubleshooting

If issues persist:

1. **Check browser cookies** - Ensure session cookies are being set with correct domain
2. **Verify APP_URL** - Must match the actual domain (https://dhtenders.com)
3. **Check HTTPS** - Ensure all requests are over HTTPS in production
4. **Session storage** - Verify session files are writable in production
5. **Check logs** - Look for authentication or session-related errors

## Files Modified

- `config/session.php` - Session security and domain configuration
- `config/cors.php` - CORS configuration for Livewire
- `config/sanctum.php` - Stateful domains for production
- `.env.production.example` - Production environment template

## Security Notes

- File uploads use existing authentication through Livewire's web middleware group
- Session cookies are secure in production (HTTPS only) when SESSION_SECURE_COOKIE=true
- CSRF protection is maintained through the web middleware group
- Session domain is properly scoped to prevent cookie leakage
- CORS credentials support enabled for proper session handling
