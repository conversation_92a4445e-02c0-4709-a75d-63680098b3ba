# Livewire File Upload 401 Unauthorized Fix

## Problem
Getting `POST https://dhtenders.com/livewire/upload-file?expires=1749809052&signature=be31d6f… 401 (Unauthorized)` error on production while file uploads work fine locally.

## Root Cause
The issue was caused by **S3 CORS configuration problems**:

### **Primary Issue: S3 CORS Configuration**
The error shows <PERSON><PERSON> is trying to upload directly from browser to S3, but the S3 bucket lacks proper CORS headers:

```
Access to XMLHttpRequest at 'https://etender-dataset.s3.ap-south-1.amazonaws.com/livewire-tmp/...'
from origin 'http://localhost:8000' has been blocked by CORS policy:
No 'Access-Control-Allow-Origin' header is present on the requested resource.
```

**Specific Issues:**
1. **Livewire configured to use S3** (`'disk' => 's3'`) for temporary uploads
2. **S3 bucket missing CORS configuration** to allow browser uploads
3. **Direct browser-to-S3 uploads** require specific CORS headers that aren't configured

### **Secondary Issues: Session/Domain Configuration** (if CORS was fixed)
4. **Missing APP_URL configuration** - Production environment didn't have proper APP_URL set
5. **Session domain mismatch** - Session cookies weren't configured for the production domain
6. **HTTPS/Secure cookie settings** - Production uses HTTPS but session cookies weren't configured as secure

## Solution

### **Primary Fix: S3 CORS Configuration**

### 1. Updated Livewire Configuration (`config/livewire.php`) - **CRITICAL FIX**
- Changed `'disk' => 's3'` to `'disk' => 'local'` for temporary uploads
- This completely avoids S3 CORS issues by using local storage for temporary files
- Final file storage still uses S3 through specific disk configurations
- **Benefits**: No CORS config needed, faster uploads, no S3 costs for temp files

### 2. Updated Production Environment (`.env.production.example`)
- Added missing `AWS_URL` configuration
- Ensured AWS credentials match production S3 bucket
- Set proper AWS region configuration

### **Secondary Fixes: Session/Domain Configuration**

### 3. Updated Session Configuration (`config/session.php`)
- Made `SESSION_SECURE_COOKIE` configurable via environment variable (defaults to false for local)
- Made `SESSION_SAME_SITE_COOKIE` configurable via environment variable

### 4. Updated CORS Configuration (`config/cors.php`)
- Added `livewire/*` to allowed paths
- Enabled `supports_credentials` for session-based authentication

### 5. Updated Sanctum Configuration (`config/sanctum.php`)
- Added production domains (`dhtenders.com`, `www.dhtenders.com`) to stateful domains

## Deployment Steps

### 1. Update Production Environment Variables
Copy the settings from `.env.production.example` to your production environment:

```bash
# Core Configuration
APP_URL=https://dhtenders.com

# AWS S3 Configuration (CRITICAL)
AWS_ACCESS_KEY_ID=********************
AWS_SECRET_ACCESS_KEY=wQpBTljFbgTampiXXB3yxwxHN6Bdx+BjkvC3wxtz
AWS_DEFAULT_REGION=us-east-1
AWS_BUCKET=etender
AWS_URL=https://etender.s3.us-east-1.amazonaws.com

# Session Configuration
SESSION_DOMAIN=.dhtenders.com
SESSION_SECURE_COOKIE=true
SESSION_SAME_SITE_COOKIE=lax

# Sanctum Configuration
SANCTUM_STATEFUL_DOMAINS=dhtenders.com,www.dhtenders.com
```

### 2. Deploy the Code Changes
Use your existing deployment process:

```bash
./deploy.sh
```

### 3. Clear Caches (if needed)
If you have access to the production server:

```bash
php artisan config:clear
php artisan config:cache
php artisan route:clear
php artisan route:cache
```

## Testing

After deployment, test the following:

1. **Clear browser cache** and refresh the page
2. **Login to the application**
3. **Navigate to a page with file upload** (e.g., tender document upload)
4. **Try uploading a file** - should work without CORS errors
5. **Check browser network tab** - uploads should go to `/livewire/upload-file`, NOT S3 URLs
6. **Verify no S3 CORS errors** in browser console

## Troubleshooting

### **S3-Related Issues:**
1. **Run S3 test script**: `php test-s3-config.php` to verify S3 connectivity
2. **Check AWS credentials** - Ensure they have S3 permissions (s3:PutObject, s3:GetObject, s3:DeleteObject)
3. **Verify S3 bucket** - Ensure bucket exists and is in the correct region
4. **Check AWS_URL** - Must match the bucket region and name

### **Session-Related Issues:**
5. **Check browser cookies** - Ensure session cookies are being set with correct domain
6. **Verify APP_URL** - Must match the actual domain (https://dhtenders.com)
7. **Check HTTPS** - Ensure all requests are over HTTPS in production
8. **Session storage** - Verify session files are writable in production
9. **Check logs** - Look for authentication or session-related errors

### **Quick Fix:**
✅ **IMPLEMENTED**: Livewire now uses local storage for temporary uploads, completely avoiding S3 CORS issues.

### **If you still see S3 URLs in errors:**
1. **Clear config cache**: `php artisan config:clear`
2. **Restart server**: Restart your development server
3. **Hard refresh browser**: Clear browser cache and refresh page

## Files Modified

- `config/livewire.php` - **CRITICAL**: Changed disk from null to 'local' for temporary uploads
- `config/session.php` - Session security and domain configuration
- `config/cors.php` - CORS configuration for Livewire
- `config/sanctum.php` - Stateful domains for production
- `.env.production.example` - Production environment template with AWS_URL
- `test-s3-config.php` - S3 connectivity test script

## Security Notes

- File uploads use existing authentication through Livewire's web middleware group
- Session cookies are secure in production (HTTPS only) when SESSION_SECURE_COOKIE=true
- CSRF protection is maintained through the web middleware group
- Session domain is properly scoped to prevent cookie leakage
- CORS credentials support enabled for proper session handling
