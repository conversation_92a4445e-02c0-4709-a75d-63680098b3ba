<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class LivewireUploadAuth
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse)  $next
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function handle(Request $request, Closure $next)
    {
        // Check if user is authenticated
        if (!Auth::check()) {
            return response()->json([
                'message' => 'Unauthenticated. Please log in to upload files.'
            ], 401);
        }

        // Verify CSRF token for file uploads
        if ($request->isMethod('post') && !$request->hasValidSignature()) {
            // For Livewire uploads, we need to ensure the session is valid
            if (!$request->session()->isValidId($request->session()->getId())) {
                return response()->json([
                    'message' => 'Invalid session. Please refresh the page and try again.'
                ], 401);
            }
        }

        return $next($request);
    }
}
