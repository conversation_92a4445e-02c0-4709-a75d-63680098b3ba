# Production Deployment Fix for Livewire CORS Issue

## Problem Identified
The production environment is using **AWS ECS with S3-stored environment files**, and the configuration is cached during deployment. The issue is:

1. **Environment loaded from S3**: `arn:aws:s3:::etender-laravel-env/env_${BranchName}.env`
2. **Configuration cached**: `docker-entrypoint.sh` runs `php artisan config:cache` 
3. **Old Livewire config cached**: S3 environment file may not have the latest changes

## Immediate Fix Steps

### Step 1: Update S3 Environment File
The production environment loads from S3. You need to update the S3 environment file:

```bash
# Find your branch name (likely 'main' or 'master')
# Update the file: s3://etender-laravel-env/env_${BranchName}.env

# Add this line to force Livewire to use local storage:
LIVEWIRE_TEMPORARY_UPLOAD_DISK=local
```

### Step 2: Update docker-entrypoint.sh for Better Cache Handling
The current entrypoint caches config immediately. We need to ensure it uses the latest config:

```bash
# In docker-entrypoint.sh, after clearing caches but before caching:
echo "Verifying Livewire configuration..."
php -r "
require 'vendor/autoload.php';
\$app = require 'bootstrap/app.php';
\$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();
echo 'Livewire disk: ' . (config('livewire.temporary_file_upload.disk') ?: 'default') . PHP_EOL;
"
```

### Step 3: Force Environment Variable Override
Add this to your S3 environment file to override the Livewire configuration:

```bash
# Force Livewire to use local storage (add to S3 env file)
LIVEWIRE_TEMPORARY_UPLOAD_DISK=local
```

## Code Changes Needed

### 1. Update Livewire Config to Use Environment Variable
```php
// config/livewire.php
'temporary_file_upload' => [
    'disk' => env('LIVEWIRE_TEMPORARY_UPLOAD_DISK', 'local'),
    // ... rest of config
],
```

### 2. Update docker-entrypoint.sh for Better Debugging
```bash
# Add after line 15 in docker-entrypoint.sh
echo "Checking Livewire configuration..."
php -r "
require 'vendor/autoload.php';
\$app = require 'bootstrap/app.php';
\$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();
\$disk = config('livewire.temporary_file_upload.disk');
echo 'Livewire upload disk: ' . (\$disk ?: 'default') . PHP_EOL;
if (\$disk === 'local') {
    echo '✅ GOOD: Using local storage' . PHP_EOL;
} else {
    echo '❌ ISSUE: Using ' . \$disk . ' storage' . PHP_EOL;
}
"
```

## Quick Production Fix (Without Code Changes)

### Option 1: Update S3 Environment File Directly
1. **Access AWS S3 Console**
2. **Navigate to bucket**: `etender-laravel-env`
3. **Find your environment file**: `env_main.env` (or `env_master.env`)
4. **Download and edit** the file
5. **Add this line**: `LIVEWIRE_TEMPORARY_UPLOAD_DISK=local`
6. **Upload back to S3**
7. **Redeploy** using `./deploy.sh`

### Option 2: Force Redeploy with Environment Override
```bash
# Deploy with environment override
./deploy.sh

# Then manually update the ECS task definition to include:
# Environment variable: LIVEWIRE_TEMPORARY_UPLOAD_DISK=local
```

## Verification Steps

After deployment, check the logs for:
```
Starting Laravel application setup...
Clearing application caches...
Checking Livewire configuration...
Livewire upload disk: local
✅ GOOD: Using local storage
```

If you see `❌ ISSUE: Using s3 storage`, the S3 environment file still needs updating.

## Root Cause Analysis

The issue occurs because:
1. **Code changes** updated `config/livewire.php` locally
2. **Production uses S3 environment files** that override local config
3. **Configuration is cached** during deployment
4. **S3 environment file** doesn't have the latest Livewire settings

## Long-term Solution

1. **Make Livewire config environment-driven** (use `env()` helper)
2. **Update S3 environment files** with proper Livewire settings
3. **Add configuration verification** to deployment process
4. **Document environment file management** for future deployments

This ensures that production configuration changes are properly managed through the S3 environment files rather than just code changes.
