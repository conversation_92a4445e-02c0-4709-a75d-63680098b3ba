<?php

/**
 * Test script to verify Livewire configuration for production
 * Run this script after deployment to verify configuration
 */

echo "=== Livewire Configuration Test ===\n\n";

// Test 1: Check APP_URL
echo "1. APP_URL Configuration:\n";
$appUrl = env('APP_URL');
echo "   APP_URL: " . ($appUrl ?: 'NOT SET') . "\n";
if (empty($appUrl)) {
    echo "   ❌ ERROR: APP_URL is not set\n";
} elseif (strpos($appUrl, 'https://') === 0) {
    echo "   ✅ OK: APP_URL uses HTTPS\n";
} else {
    echo "   ⚠️  WARNING: APP_URL should use HTTPS in production\n";
}
echo "\n";

// Test 2: Check Session Configuration
echo "2. Session Configuration:\n";
$sessionDomain = env('SESSION_DOMAIN');
$sessionSecure = env('SESSION_SECURE_COOKIE');
$sessionSameSite = env('SESSION_SAME_SITE_COOKIE', 'lax');

echo "   SESSION_DOMAIN: " . ($sessionDomain ?: 'NOT SET') . "\n";
echo "   SESSION_SECURE_COOKIE: " . ($sessionSecure ? 'true' : 'false') . "\n";
echo "   SESSION_SAME_SITE_COOKIE: " . $sessionSameSite . "\n";

if (empty($sessionDomain)) {
    echo "   ⚠️  WARNING: SESSION_DOMAIN not set - may cause issues with subdomains\n";
} else {
    echo "   ✅ OK: SESSION_DOMAIN is configured\n";
}

if (env('APP_ENV') === 'production' && !$sessionSecure) {
    echo "   ❌ ERROR: SESSION_SECURE_COOKIE should be true in production\n";
} else {
    echo "   ✅ OK: Session security configuration looks good\n";
}
echo "\n";

// Test 3: Check Sanctum Configuration
echo "3. Sanctum Configuration:\n";
$sanctumDomains = env('SANCTUM_STATEFUL_DOMAINS');
echo "   SANCTUM_STATEFUL_DOMAINS: " . ($sanctumDomains ?: 'NOT SET') . "\n";

if (empty($sanctumDomains)) {
    echo "   ⚠️  WARNING: SANCTUM_STATEFUL_DOMAINS not set\n";
} elseif (strpos($sanctumDomains, 'dhtenders.com') !== false) {
    echo "   ✅ OK: Production domain included in stateful domains\n";
} else {
    echo "   ❌ ERROR: Production domain not found in stateful domains\n";
}
echo "\n";

// Test 4: Check Livewire Configuration
echo "4. Livewire Configuration:\n";
$livewireConfig = config('livewire.temporary_file_upload');
$livewireDisk = $livewireConfig['disk'] ?: config('filesystems.default');
echo "   Upload disk: " . $livewireDisk . "\n";
echo "   Middleware: " . ($livewireConfig['middleware'] ?: 'null (default throttling)') . "\n";
echo "   Max upload time: " . $livewireConfig['max_upload_time'] . " minutes\n";

if ($livewireDisk === 'local') {
    echo "   ✅ OK: Using local disk (recommended for temporary uploads)\n";
} elseif ($livewireDisk === 's3') {
    echo "   ⚠️  WARNING: Using S3 disk - ensure AWS credentials have proper permissions\n";
} else {
    echo "   ✅ OK: Livewire file upload configuration loaded\n";
}
echo "\n";

// Test 5: Check CORS Configuration
echo "5. CORS Configuration:\n";
$corsConfig = config('cors');
$livewireInPaths = in_array('livewire/*', $corsConfig['paths']);
$credentialsSupported = $corsConfig['supports_credentials'];

echo "   Livewire paths included: " . ($livewireInPaths ? 'YES' : 'NO') . "\n";
echo "   Credentials supported: " . ($credentialsSupported ? 'YES' : 'NO') . "\n";

if (!$livewireInPaths) {
    echo "   ❌ ERROR: livewire/* not in CORS paths\n";
} else {
    echo "   ✅ OK: Livewire paths included in CORS\n";
}

if (!$credentialsSupported) {
    echo "   ❌ ERROR: CORS credentials not supported\n";
} else {
    echo "   ✅ OK: CORS credentials supported\n";
}
echo "\n";

echo "=== Test Complete ===\n";
echo "If all tests show ✅ OK, the configuration should work correctly.\n";
echo "If you see ❌ ERROR or ⚠️  WARNING, please review the configuration.\n";
echo "\n";
echo "💡 TIP: Run 'php test-s3-config.php' for detailed S3 testing.\n";
echo "💡 TIP: Livewire now uses local disk for temporary uploads to avoid S3 issues.\n";
