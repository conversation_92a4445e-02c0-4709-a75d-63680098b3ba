#!/bin/bash

echo "🚀 Deploying Livewire CORS Fix to Production"
echo "============================================="

# Step 1: Verify local changes
echo "1. Verifying local configuration changes..."

# Check if Livewire config uses environment variable
if grep -q "env('LIVEWIRE_TEMPORARY_UPLOAD_DISK'" config/livewire.php; then
    echo "   ✅ Livewire config updated to use environment variable"
else
    echo "   ❌ Livewire config not updated"
    exit 1
fi

# Check if environment file has the variable
if grep -q "LIVEWIRE_TEMPORARY_UPLOAD_DISK=local" env_develop.env; then
    echo "   ✅ Environment file has LIVEWIRE_TEMPORARY_UPLOAD_DISK=local"
else
    echo "   ❌ Environment file missing LIVEWIRE_TEMPORARY_UPLOAD_DISK"
    exit 1
fi

# Check if docker-entrypoint.sh has verification
if grep -q "Verifying Livewire configuration" docker-entrypoint.sh; then
    echo "   ✅ Docker entrypoint has Livewire verification"
else
    echo "   ❌ Docker entrypoint not updated"
    exit 1
fi

echo ""
echo "2. All local changes verified. Proceeding with deployment..."
echo ""

# Step 2: Deploy using existing script
echo "3. Running deployment..."
./deploy.sh

echo ""
echo "🎉 Deployment Complete!"
echo "======================"
echo ""
echo "Next steps:"
echo "1. Wait for deployment to complete (check AWS ECS console)"
echo "2. Check application logs for:"
echo "   'Verifying Livewire configuration...'"
echo "   'Livewire upload disk: local'"
echo "   '✅ GOOD: Using local storage'"
echo ""
echo "3. Test file upload on production:"
echo "   - Should NOT see S3 URLs in browser network tab"
echo "   - Should NOT see CORS errors in browser console"
echo "   - Uploads should go to /livewire/upload-file endpoint"
echo ""
echo "If you still see S3 CORS errors:"
echo "1. Check ECS logs for Livewire configuration output"
echo "2. Verify env_develop.env was uploaded to S3 correctly"
echo "3. Force restart ECS service to reload environment"
