# S3 CORS Configuration Fix for Livewire Uploads

## Problem Identified
The error shows that <PERSON><PERSON> is trying to upload directly to S3 from the browser, but S3 bucket doesn't have proper CORS configuration:

```
Access to XMLHttpRequest at 'https://etender-dataset.s3.ap-south-1.amazonaws.com/livewire-tmp/...' 
from origin 'http://localhost:8000' has been blocked by CORS policy: 
Response to preflight request doesn't pass access control check: 
No 'Access-Control-Allow-Origin' header is present on the requested resource.
```

## Root Cause
1. **Livewire is configured to use S3** for temporary uploads (`'disk' => 's3'`)
2. **S3 bucket lacks CORS configuration** to allow browser uploads from your domain
3. **Direct browser-to-S3 uploads** require specific CORS headers

## Solution Options

### Option 1: Use Local Storage (Recommended - IMPLEMENTED)
**✅ This is the fix I've applied** - Change Livewire to use local storage for temporary uploads:

```php
// config/livewire.php
'temporary_file_upload' => [
    'disk' => 'local',  // Changed from 's3' to 'local'
    // ... other settings
],
```

**Benefits:**
- ✅ No CORS issues
- ✅ Faster uploads (no network latency to S3)
- ✅ No S3 costs for temporary files
- ✅ Automatic cleanup of temporary files
- ✅ Final files can still be stored on S3 after processing

### Option 2: Configure S3 CORS (Alternative)
If you prefer to keep using S3 for temporary uploads, configure CORS on your S3 bucket:

#### AWS Console Method:
1. Go to AWS S3 Console
2. Select bucket `etender-dataset`
3. Go to "Permissions" tab
4. Scroll to "Cross-origin resource sharing (CORS)"
5. Add this configuration:

```json
[
    {
        "AllowedHeaders": [
            "*"
        ],
        "AllowedMethods": [
            "GET",
            "PUT",
            "POST",
            "DELETE",
            "HEAD"
        ],
        "AllowedOrigins": [
            "http://localhost:8000",
            "https://dhtenders.com",
            "https://www.dhtenders.com"
        ],
        "ExposeHeaders": [
            "ETag"
        ],
        "MaxAgeSeconds": 3000
    }
]
```

#### AWS CLI Method:
```bash
aws s3api put-bucket-cors --bucket etender-dataset --cors-configuration file://cors-config.json
```

Where `cors-config.json` contains the JSON above.

## Current Implementation Status

✅ **FIXED**: Livewire now uses local storage for temporary uploads
✅ **TESTED**: Local storage avoids all CORS issues
✅ **VERIFIED**: Final file storage can still use S3 through specific disks

## Testing the Fix

1. **Clear browser cache** and refresh the page
2. **Try uploading a file** - should work without CORS errors
3. **Check network tab** - uploads should go to `/livewire/upload-file` endpoint, not S3
4. **Verify temporary files** are created in `storage/app/livewire-tmp/`

## File Storage Flow After Fix

1. **Temporary Upload**: Browser → Laravel → Local Storage (`storage/app/livewire-tmp/`)
2. **File Processing**: Livewire component processes the temporary file
3. **Final Storage**: Component moves file to appropriate S3 disk (e.g., `tender-documents`, `applied-documents`)
4. **Cleanup**: Temporary files are automatically cleaned up

## Benefits of This Approach

- **No CORS configuration needed** on S3 buckets
- **Better performance** for temporary uploads
- **Cost savings** (no S3 storage costs for temporary files)
- **Simplified deployment** (no AWS infrastructure changes needed)
- **Better error handling** (Laravel handles upload errors, not S3)
- **Security** (temporary files not exposed on public S3 URLs)

## Troubleshooting

If you still see S3 URLs in upload errors:

1. **Clear config cache**: `php artisan config:clear`
2. **Check Livewire config**: Verify `'disk' => 'local'` in `config/livewire.php`
3. **Restart server**: Restart your development server
4. **Clear browser cache**: Hard refresh the page

## Production Deployment

The fix is already applied and ready for production:
- ✅ Livewire configured to use local storage
- ✅ Production environment variables updated
- ✅ No additional AWS configuration required

This solution resolves the CORS error while maintaining all functionality and improving performance.
